<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Control - API de Moderación de Contenido</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo i {
            font-size: 2rem;
            color: #667eea;
        }

        .logo h1 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: #e8f5e8;
            border-radius: 20px;
            border: 1px solid #4CAF50;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .service-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .service-title i {
            font-size: 1.5rem;
            color: #667eea;
        }

        .service-title h3 {
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .service-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4CAF50;
        }

        .status-inactive {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .status-checking {
            background: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ff9800;
        }

        .status-warning {
            background: #fffbf0;
            color: #f57c00;
            border: 1px solid #ffc107;
        }

        .service-description {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .service-endpoint {
            background: #f5f5f5;
            padding: 0.75rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            margin-bottom: 1rem;
            word-break: break-all;
            border-left: 3px solid #667eea;
        }



        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .testing-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .testing-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .testing-header i {
            font-size: 2rem;
            color: #667eea;
        }

        .testing-header h2 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            margin-bottom: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-area i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        .upload-area:hover i,
        .upload-area.dragover i {
            color: #667eea;
        }

        #imageInput {
            display: none;
        }

        .preview-container {
            text-align: center;
            margin: 2rem 0;
        }

        #imagePreview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .service-selector {
            margin-bottom: 2rem;
        }

        .service-selector label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .service-selector select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }

        .result-container {
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 10px;
            display: none;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .result-success {
            border-left: 4px solid #28a745;
        }

        .result-error {
            border-left: 4px solid #dc3545;
        }

        .result-warning {
            border-left: 4px solid #ffc107;
        }

        .result-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .result-title i {
            color: #667eea;
        }

        .json-content {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-word;
            color: #212529;
            line-height: 1.4;
        }

        .api-docs-toggle {
            background: none;
            border: none;
            color: #28a745;
            cursor: pointer;
            font-size: 0.8rem;
            padding: 0.25rem 0;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            margin-top: 0.5rem;
        }

        .api-docs-toggle:hover {
            color: #218838;
        }

        .api-docs-toggle i {
            transition: transform 0.3s ease;
        }

        .api-docs-toggle.expanded i {
            transform: rotate(90deg);
        }

        .api-docs-content {
            display: none;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }

        .api-example {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            overflow-x: auto;
        }

        .api-example-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
            font-family: 'Segoe UI', sans-serif;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 2rem 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer p {
            color: #666;
            margin-bottom: 0.5rem;
        }

        .footer .contact {
            color: #667eea;
            font-weight: 600;
        }

        /* Logo Model Selection Styles */
        .logo-model-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .logo-model-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .logo-model-card.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .logo-model-card .model-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .logo-model-card .model-description {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .logo-model-card.selected .model-description {
            opacity: 0.9;
        }

        /* Multi-model results styles */
        .multi-model-results {
            margin-top: 1rem;
        }

        .summary-result {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 8px 8px 0;
        }

        .summary-result.detected {
            border-left-color: #dc3545;
            background: #fff5f5;
        }

        .summary-result.safe {
            border-left-color: #28a745;
            background: #f8fff8;
        }

        .individual-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .model-result-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .model-result-card.detected {
            border-left: 4px solid #dc3545;
        }

        .model-result-card.safe {
            border-left: 4px solid #28a745;
        }

        .model-result-card.error {
            border-left: 4px solid #ffc107;
        }

        .model-result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .model-result-name {
            font-weight: 600;
            color: #333;
        }

        .model-result-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .model-result-status.detected {
            background: #dc3545;
            color: white;
        }

        .model-result-status.safe {
            background: #28a745;
            color: white;
        }

        .model-result-status.error {
            background: #ffc107;
            color: #333;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }



            .upload-area {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <h1>Panel de Control - Moderación de Contenido</h1>
            </div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Servicios Activos</span>
            </div>
        </div>
    </header>

    <!-- Mixed Content Warning Banner -->
    <div id="mixedContentBanner"
        style="display: none; background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 1rem; margin: 1rem; border-radius: 8px; text-align: center;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
            <strong>Aviso: Contenido Mixto Detectado</strong>
        </div>
        <p style="margin: 0; font-size: 0.9rem;">
            Algunos servicios (Búsqueda Semántica y Detección de Logos) usan HTTP y pueden estar bloqueados por el
            navegador desde esta página HTTPS.
            Para probar estos servicios, accede directamente a sus endpoints o usa herramientas como Postman.
        </p>
        <button onclick="document.getElementById('mixedContentBanner').style.display='none'"
            style="background: none; border: none; color: #856404; cursor: pointer; text-decoration: underline; margin-top: 0.5rem;">
            Entendido, ocultar este mensaje
        </button>
    </div>

    <!-- Main Container -->
    <div class="container">

        <!-- Testing Panel -->
        <div class="testing-panel">
            <div class="testing-header">
                <i class="fas fa-flask"></i>
                <h2>Panel de Pruebas Interactivo</h2>
            </div>

            <div class="service-selector">
                <label for="serviceSelect">Seleccionar Servicio:</label>
                <select id="serviceSelect">
                    <option value="moderate-image">Moderación de Imágenes</option>
                    <option value="age-verification">Verificación de Edad</option>
                    <option value="image-text-validator">Validador Imagen-Texto</option>
                </select>
            </div>



            <div class="upload-area" onclick="document.getElementById('imageInput').click()">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>Subir Imagen para Prueba</h3>
                <p>Haz clic aquí o arrastra una imagen para subirla</p>
                <p style="font-size: 0.9rem; color: #666; margin-top: 1rem;">
                    Formatos soportados: JPG, PNG, GIF | Tamaño máximo: 5MB
                </p>
            </div>

            <input type="file" id="imageInput" accept="image/*">

            <div class="preview-container">
                <img id="imagePreview" style="display: none;">
            </div>

            <div id="additionalInputs" style="display: none;">
                <div style="margin-bottom: 1rem;">
                    <label for="titleInput"
                        style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Título:</label>
                    <input type="text" id="titleInput" placeholder="Ingrese el título de la imagen"
                        style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label for="descriptionInput"
                        style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Descripción:</label>
                    <textarea id="descriptionInput" placeholder="Ingrese la descripción de la imagen"
                        style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; min-height: 100px; resize: vertical;"></textarea>
                </div>
            </div>

            <div style="text-align: center; margin: 2rem 0;">
                <button id="testSelectedButton" class="btn btn-success" onclick="testSelectedService()" disabled>
                    <i class="fas fa-play"></i> Probar Servicio Seleccionado
                </button>
                <button class="btn btn-secondary" onclick="clearTest()" style="margin-left: 1rem;">
                    <i class="fas fa-trash"></i> Limpiar
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Procesando solicitud...</p>
            </div>

            <div class="result-container" id="resultContainer">
                <div class="result-title">
                    <i class="fas fa-code"></i>
                    <span>Respuesta del Servicio</span>
                </div>
                <div class="json-content" id="jsonContent"></div>
            </div>
        </div>
    </div>

    <!-- Services Grid -->
    <div class="services-grid">

        <!-- Gali Agent Service -->
        <div class="service-card">
            <div class="service-header">
                <div class="service-title">
                    <i class="fas fa-robot"></i>
                    <h3>Gali - Agente IA Dropi</h3>
                </div>
            </div>
            <p class="service-description">
                Un agente de IA conversacional que responde preguntas sobre el funcionamiento de Dropi.
                Entrenado con la documentación interna para dar soporte.
            </p>
            <div class="service-endpoint" id="gali-endpoint">
                http://*************:8000/docs
            </div>
            <div style="margin-top: auto;">
                <button class="btn btn-primary" onclick="openGaliAgentInterface()">
                    <i class="fas fa-external-link-alt"></i> Abrir Interfaz
                </button>
            </div>
        </div>

        <!-- Semantica Service -->
        <div class="service-card">
            <div class="service-header">
                <div class="service-title">
                    <i class="fa-brands fa-searchengin"></i>
                    <h3>Búsqueda Semántica</h3>
                </div>
            </div>
            <p class="service-description">
                Búsqueda inteligente de imágenes basada en contenido semántico.
                Utiliza embeddings de IA para encontrar imágenes similares por contexto.
            </p>
            <div class="service-endpoint" id="gali-endpoint">
                http://*************:8000/docs
            </div>
            <div style="margin-top: auto;">
                <button class="btn btn-primary" onclick="openSemanticSearchInterface()">
                    <i class="fas fa-external-link-alt"></i> Abrir Interfaz
                </button>
            </div>
        </div>

        <!-- Moderate Image Service -->
        <div class="service-card">
            <div class="service-header">
                <div class="service-title">
                    <i class="fas fa-eye"></i>
                    <h3>Moderación de Imágenes</h3>
                </div>
                <span class="service-status status-checking" id="moderate-image-status">Verificando</span>
            </div>
            <p class="service-description">
                Detecta contenido inapropiado en imágenes utilizando Amazon Rekognition.
                Identifica violencia, desnudez, contenido sugestivo y otros elementos no deseados.
            </p>
            <div class="service-endpoint">
                https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/moderate-image
            </div>

            <button class="api-docs-toggle" onclick="toggleApiDocs('moderate-image')">
                <i class="fas fa-chevron-right"></i> Ver Documentación API
            </button>
            <div class="api-docs-content" id="moderate-image-docs">
                <div class="api-example-title">📤 Formato de Solicitud:</div>
                <div class="api-example">{
                    "body": {
                    "image": "[base64-encoded-image-string]"
                    }
                    }</div>
                <div class="api-example-title">📥 Formato de Respuesta:</div>
                <div class="api-example">{
                    "moderation_result": "APPROVED" | "REJECTED",
                    "detected_labels": ["Violence", "Nudity", ...],
                    "confidence_scores": {
                    "Violence": 95.5,
                    "Nudity": 87.2
                    },
                    "message": "Contenido apropiado detectado"
                    }</div>
            </div>
        </div>

        <!-- Age Verification Service -->
        <div class="service-card">
            <div class="service-header">
                <div class="service-title">
                    <i class="fas fa-child"></i>
                    <h3>Verificación de Edad</h3>
                </div>
                <span class="service-status status-checking" id="age-verification-status">Verificando</span>
            </div>
            <p class="service-description">
                Detecta la presencia de menores de edad en imágenes utilizando Amazon Rekognition.
                Ayuda a cumplir con regulaciones de protección infantil.
            </p>
            <div class="service-endpoint">
                https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/age-verification
            </div>

            <button class="api-docs-toggle" onclick="toggleApiDocs('age-verification')">
                <i class="fas fa-chevron-right"></i> Ver Documentación API
            </button>
            <div class="api-docs-content" id="age-verification-docs">
                <div class="api-example-title">📤 Formato de Solicitud:</div>
                <div class="api-example">{
                    "body": {
                    "image": "[base64-encoded-image-string]"
                    }
                    }</div>
                <div class="api-example-title">📥 Formato de Respuesta:</div>
                <div class="api-example">{
                    "minors_detected": true | false,
                    "confidence": 85.7,
                    "age_ranges": [
                    {
                    "low": 15,
                    "high": 20,
                    "confidence": 85.7
                    }
                    ],
                    "message": "Se detectaron posibles menores de edad"
                    }</div>
            </div>
        </div>

        <!-- Image Text Validator Service -->
        <div class="service-card">
            <div class="service-header">
                <div class="service-title">
                    <i class="fas fa-search"></i>
                    <h3>Validador Imagen-Texto</h3>
                </div>
                <span class="service-status status-checking" id="image-text-validator-status">Verificando</span>
            </div>
            <p class="service-description">
                Valida la coherencia entre imágenes y texto descriptivo utilizando Google Gemini AI.
                Detecta discrepancias entre el contenido visual y textual.
            </p>
            <div class="service-endpoint">
                https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/image-text-validator
            </div>

            <button class="api-docs-toggle" onclick="toggleApiDocs('image-text-validator')">
                <i class="fas fa-chevron-right"></i> Ver Documentación API
            </button>
            <div class="api-docs-content" id="image-text-validator-docs">
                <div class="api-example-title">📤 Formato de Solicitud:</div>
                <div class="api-example">{
                    "body": {
                    "image": "[base64-encoded-image-string]",
                    "title": "Título del producto",
                    "description": "Descripción detallada del producto"
                    }
                    }</div>
                <div class="api-example-title">📥 Formato de Respuesta:</div>
                <div class="api-example">{
                    "concordance_score": 85,
                    "analysis": "La imagen muestra un producto que coincide con la descripción",
                    "discrepancies": [],
                    "categories_detected": ["electronics", "smartphone"],
                    "validation_result": "COHERENT" | "INCOHERENT",
                    "confidence": 92.5
                    }</div>
            </div>
        </div>



        <!-- Logo Detection Service -->
        <!-- <div class="service-card">
                <div class="service-header">
                    <div class="service-title">
                        <i class="fas fa-trademark"></i>
                        <h3>Detección de Logos</h3>
                    </div>
                    <span class="service-status status-checking" id="logo-detection-status">Verificando</span>
                </div>
                <p class="service-description">
                    Detecta logos prohibidos en imágenes utilizando 6 modelos de machine learning especializados.
                    Soporta análisis con modelos individuales o todos los modelos simultáneamente.
                    Desplegado en AWS Fargate optimizado para costos (256 CPU, 1024 MiB) con acceso directo.
                    Modelos disponibles: flag_premium, flag_premium_exclusivo, flag_verificado, insignia_premium, insignia_premium_verificado, insignia_verificado.
                </p>
                <div class="service-endpoint">
                    http://**************:8080
                </div>

                <button class="btn btn-primary" onclick="openLogoDetectionInterface()" style="margin-top: 1rem; margin-right: 1rem;">
                    <i class="fas fa-external-link-alt"></i> 🔍 Abrir Interfaz de Detección
                </button>
                <button class="api-docs-toggle" onclick="toggleApiDocs('logo-detection')">
                    <i class="fas fa-chevron-right"></i> Ver Documentación API
                </button>
                <div class="api-docs-content" id="logo-detection-docs">
                    <div class="api-example-title">📤 Formato de Solicitud (Modelo Individual):</div>
                    <div class="api-example">{
  "body": {
    "image": "[base64-encoded-image-string]"
  }
}</div>
                    <div class="api-example-title">📥 Formato de Respuesta (Modelo Individual):</div>
                    <div class="api-example">{
  "result": true | false,
  "message": "Logo prohibido detectado" | "No se detectó logo prohibido"
}</div>
                    <div class="api-example-title">🔍 Análisis con Todos los Modelos:</div>
                    <div class="api-example">Endpoint: /detect-all-models
Respuesta:
{
  "results": {
    "verificado": {
      "result": false,
      "message": "No se detectó logo prohibido",
      "confidence": 25.3
    },
    "premium": {
      "result": true,
      "message": "Logo prohibido detectado",
      "confidence": 78.9
    }
  },
  "summary": {
    "overall_result": true,
    "detected_by_models": ["premium"],
    "total_models_tested": 4,
    "models_with_detection": 1,
    "message": "Logo detectado por 1 out of 4 modelos"
  }
}</div>
                    <div class="api-example-title">📋 Modelos Disponibles:</div>
                    <div class="api-example">Endpoint: /models
Respuesta:
{
  "available_models": [
    "flag_premium",
    "flag_premium_exclusivo",
    "flag_verificado",
    "insignia_premium",
    "insignia_premium_verificado",
    "insignia_verificado"
  ],
  "total_models": 6
}</div>
                </div>
            </div> -->

        <!-- Semantic Search Service -->
        <!-- <div class="service-card">
                <div class="service-header">
                    <div class="service-title">
                        <i class="fas fa-brain"></i>
                        <h3>Búsqueda Semántica</h3>
                    </div>
                    <span class="service-status status-checking" id="semantic-search-status">Verificando</span>
                </div>
                <p class="service-description">
                    Búsqueda inteligente de imágenes basada en contenido semántico.
                    Utiliza embeddings de IA para encontrar imágenes similares por contexto.
                </p>
                <div class="service-endpoint" id="semantic-search-endpoint">
                    http://***********:8000
                </div>

                <button class="btn btn-primary" onclick="openSemanticSearchInterface()" style="margin-top: 1rem; margin-right: 1rem;">
                    <i class="fas fa-external-link-alt"></i> 🌐 Abrir en Nueva Pestaña
                </button>
                <button class="api-docs-toggle" onclick="toggleApiDocs('semantic-search')">
                    <i class="fas fa-chevron-right"></i> Ver Documentación API
                </button>
                <div class="api-docs-content" id="semantic-search-docs">
                    <div class="api-example-title">📤 Subir Imagen (POST /upload-image):</div>
                    <div class="api-example">Content-Type: multipart/form-data
{
  "file": "[image-file]",
  "precio": 29.99
}</div>
                    <div class="api-example-title">📥 Respuesta de Subida:</div>
                    <div class="api-example">{
  "id": 0,
  "nombre": "producto.jpg",
  "precio": 29.99,
  "descripcion": "Descripción generada por IA",
  "imagen_base64": "[base64-string]"
}</div>
                    <div class="api-example-title">🔍 Búsqueda Semántica (POST /search):</div>
                    <div class="api-example">{
  "query": "busco una camiseta azul entre 20 y 50 pesos"
}</div>
                    <div class="api-example-title">📥 Respuesta de Búsqueda:</div>
                    <div class="api-example">{
  "query_original": "busco una camiseta azul entre 20 y 50 pesos",
  "query_limpia": "camiseta azul",
  "precios_info": "Entre 20 y 50 pesos",
  "resultados": [
    {
      "id": 0,
      "nombre": "camiseta.jpg",
      "precio": 35.0,
      "descripcion": "Camiseta azul de algodón",
      "imagen_base64": "[base64-string]",
      "distancia": 0.23
    }
  ]
}</div>
                    <div class="api-example-title">📋 Ver Todas las Imágenes (GET /images):</div>
                    <div class="api-example">Retorna todas las imágenes almacenadas con sus metadatos</div>
                </div>
            </div> -->
    </div>



    <!-- Footer -->
    <footer class="footer">
        <p>API de Moderación de Contenido - Dropi</p>
        <p class="contact">Contacto: <EMAIL> | Juan Felipe Cubillos Prado</p>
        <p style="font-size: 0.9rem; color: #999; margin-top: 1rem;">
            Servicios desplegados en AWS | Región: us-east-2
        </p>
    </footer>

    <script>
        // Service endpoints configuration
        const serviceEndpoints = {
            'moderate-image': 'https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/moderate-image',
            'age-verification': 'https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/age-verification',
            'image-text-validator': 'https://5qajayhcr3.execute-api.us-east-2.amazonaws.com/v1/api/v1/image-text-validator',
            'logo-detection': 'http://**************:8080',
            'semantic-search': 'http://***********:8000/upload-image',
            'gali-agent': 'http://*************:8000'
        };

        let selectedImage = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function () {
            setupEventListeners();
            checkAllServicesHealth();

            // Show mixed content banner after a short delay to let health checks complete
            setTimeout(showMixedContentBannerIfNeeded, 2000);

            // Check service health every 30 seconds
            setInterval(checkAllServicesHealth, 30000);
        });

        function showMixedContentBannerIfNeeded() {
            // Check if any HTTP services have warning status
            const semanticSearchStatus = document.getElementById('semantic-search-status');
            const logoDetectionStatus = document.getElementById('logo-detection-status');

            if ((semanticSearchStatus && semanticSearchStatus.className.includes('status-warning')) ||
                (logoDetectionStatus && logoDetectionStatus.className.includes('status-warning'))) {
                document.getElementById('mixedContentBanner').style.display = 'block';
            }
        }

        function setupEventListeners() {
            const uploadArea = document.querySelector('.upload-area');
            const imageInput = document.getElementById('imageInput');
            const serviceSelect = document.getElementById('serviceSelect');

            // Drag and drop events
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageSelect(files[0]);
                }
            });

            // File input change
            imageInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleImageSelect(e.target.files[0]);
                }
            });

            // Service selection change
            serviceSelect.addEventListener('change', (e) => {
                toggleAdditionalInputs(e.target.value);
            });
        }

        function handleImageSelect(file) {
            if (!file.type.startsWith('image/')) {
                showResult('error', { error: 'Tipo de archivo inválido', message: 'Por favor selecciona un archivo de imagen válido.' });
                return;
            }

            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                showResult('error', { error: 'Archivo demasiado grande', message: 'El archivo es demasiado grande. El tamaño máximo es 5MB.', size: file.size });
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                selectedImage = e.target.result.split(',')[1]; // Get base64 part only
                const imagePreview = document.getElementById('imagePreview');
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                document.getElementById('testSelectedButton').disabled = false;
                updateTestAllModelsButton(); // Update the logo models button
                hideResult();
            };
            reader.readAsDataURL(file);
        }

        function toggleAdditionalInputs(service) {
            const additionalInputs = document.getElementById('additionalInputs');

            if (service === 'image-text-validator') {
                additionalInputs.style.display = 'block';
            } else {
                additionalInputs.style.display = 'none';
            }
        }









        async function checkAllServicesHealth() {
            const services = ['gali', 'moderate-image', 'age-verification', 'image-text-validator'];

            for (const service of services) {
                await checkHealth(service);
            }
        }

        async function checkHealth(serviceName) {
            console.log(serviceName);

            const statusElement = document.getElementById(`${serviceName}-status`);
            statusElement.textContent = 'Verificando';
            statusElement.className = 'service-status status-checking';

            try {
                let healthUrl;
                let method = 'OPTIONS';

                if (serviceName === 'semantic-search') {
                    healthUrl = 'http://***********:8000/health';
                    method = 'GET';
                } else if (serviceName === 'logo-detection') {
                    healthUrl = 'http://**************:8080/health';
                    method = 'GET';
                } else if (serviceName === 'gali') {
                    healthUrl = 'http://*************:8000/health';
                    method = 'GET';
                } else {
                    // For Lambda functions, we'll do a simple OPTIONS request to check CORS
                    healthUrl = serviceEndpoints[serviceName];
                }

                const response = await fetch(healthUrl, {
                    method: method,
                    mode: 'cors'
                });

                if (response.ok || response.status === 200) {
                    statusElement.textContent = 'Activo';
                    statusElement.className = 'service-status status-active';
                } else {
                    statusElement.textContent = 'Inactivo';
                    statusElement.className = 'service-status status-inactive';
                }
            } catch (error) {
                console.error(`Health check failed for ${serviceName}:`, error);

                // Check if it's a mixed content error
                if (error.message && error.message.includes('Mixed Content')) {
                    statusElement.textContent = 'HTTP/HTTPS';
                    statusElement.className = 'service-status status-warning';
                    statusElement.title = 'Servicio disponible pero requiere acceso directo por protocolo HTTP';
                } else if (serviceName === 'semantic-search' || serviceName === 'logo-detection') {
                    // For HTTP services, assume they might be blocked by mixed content
                    statusElement.textContent = 'HTTP/HTTPS';
                    statusElement.className = 'service-status status-warning';
                    statusElement.title = 'Servicio HTTP bloqueado por política de contenido mixto. Use acceso directo.';
                } else {
                    statusElement.textContent = 'Error';
                    statusElement.className = 'service-status status-inactive';
                }
            }
        }



        async function testSelectedService() {
            const selectedService = document.getElementById('serviceSelect').value;

            // Clear any previous results and scroll to result area
            hideResult();

            // Scroll to result area for better UX
            const resultContainer = document.getElementById('resultContainer');
            if (resultContainer) {
                resultContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            if (!selectedImage) {
                // Show loading briefly for visual feedback
                showLoading(true);

                // Small delay to show the loading state
                setTimeout(() => {
                    showLoading(false);
                    showResult('error', { error: 'Imagen requerida', message: 'Por favor selecciona una imagen primero.' });
                }, 300);
                return;
            }

            if (selectedService === 'image-text-validator') {
                const title = document.getElementById('titleInput').value;
                const description = document.getElementById('descriptionInput').value;

                if (!title || !description) {
                    showResult('error', { error: 'Campos requeridos', message: 'Por favor ingresa título y descripción para el validador imagen-texto.' });
                    return;
                }
            }

            showLoading(true);
            hideResult();

            try {
                const endpoint = serviceEndpoints[selectedService];
                let requestBody;
                let fetchUrl = endpoint;

                if (selectedService === 'image-text-validator') {
                    const title = document.getElementById('titleInput').value;
                    const description = document.getElementById('descriptionInput').value;

                    requestBody = {
                        body: {
                            image: selectedImage,
                            title: title,
                            description: description
                        }
                    };
                } else if (selectedService === 'semantic-search') {
                    requestBody = {
                        image: selectedImage
                    };
                } else {
                    requestBody = {
                        body: {
                            image: selectedImage
                        }
                    };
                }

                const response = await fetch(fetchUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify(requestBody)
                });

                const result = await response.json();

                if (response.ok) {
                    showResult('success', result);
                } else {
                    showResult('error', result);
                }
            } catch (error) {
                console.error('Service test error:', error);

                // Check if it's a mixed content error for HTTP services
                if (selectedService === 'semantic-search' &&
                    (error.message.includes('Mixed Content') || error.message.includes('Failed to fetch'))) {

                    const endpoint = serviceEndpoints[selectedService];
                    showResult('warning', {
                        error: 'Contenido Mixto Bloqueado',
                        message: `El navegador bloqueó la solicitud HTTP desde una página HTTPS. Para probar este servicio:`,
                        solutions: [
                            `1. Accede directamente al servicio: ${endpoint}`,
                            `2. Usa herramientas como Postman o curl`,
                            `3. Configura el servicio para usar HTTPS`,
                            `4. Usa un proxy HTTPS para el servicio`
                        ],
                        technical_details: {
                            service: selectedService,
                            endpoint: endpoint,
                            error_type: 'Mixed Content Policy',
                            browser_policy: 'Los navegadores bloquean solicitudes HTTP desde páginas HTTPS por seguridad'
                        },
                        timestamp: new Date().toISOString()
                    });
                } else {
                    showResult('error', {
                        error: 'Error de conexión',
                        message: error.message,
                        service: selectedService,
                        timestamp: new Date().toISOString()
                    });
                }
            } finally {
                showLoading(false);
            }
        }



        function showResult(type, rawResponse = null) {
            const container = document.getElementById('resultContainer');
            const jsonContent = document.getElementById('jsonContent');

            container.className = `result-container result-${type}`;

            // Display raw JSON response
            if (rawResponse) {
                jsonContent.textContent = JSON.stringify(rawResponse, null, 2);
            } else {
                jsonContent.textContent = 'No se recibió respuesta del servicio.';
            }

            container.style.display = 'block';
        }



        function hideResult() {
            document.getElementById('resultContainer').style.display = 'none';
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            const testButton = document.getElementById('testSelectedButton');
            if (testButton) {
                testButton.disabled = show;
            }
        }

        function clearTest() {
            selectedImage = null;
            const imagePreview = document.getElementById('imagePreview');
            imagePreview.style.display = 'none';
            document.getElementById('testSelectedButton').disabled = true;
            document.getElementById('imageInput').value = '';
            document.getElementById('titleInput').value = '';
            document.getElementById('descriptionInput').value = '';

            hideResult();
        }

        function toggleApiDocs(serviceName) {
            const toggle = document.querySelector(`[onclick="toggleApiDocs('${serviceName}')"]`);
            const content = document.getElementById(`${serviceName}-docs`);
            const icon = toggle.querySelector('i');

            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggle.classList.add('expanded');
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
                toggle.innerHTML = '<i class="fas fa-chevron-down"></i> Ocultar Documentación API';
            } else {
                content.style.display = 'none';
                toggle.classList.remove('expanded');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
                toggle.innerHTML = '<i class="fas fa-chevron-right"></i> Ver Documentación API';
            }
        }

        function openSemanticSearchInterface() {
            // Open the semantic search web interface in a new tab
            window.open('http://*************:8000/', '_blank');
        }

        function openGaliAgentInterface() {
            // Open the semantic search web interface in a new tab
            window.open('http://*************:8000/', '_blank');
        }

        function openLogoDetectionInterface() {
            // Open the logo detection web interface in a new tab
            window.open('http://**************:8080/', '_blank');
        }


    </script>

</body>

</html>