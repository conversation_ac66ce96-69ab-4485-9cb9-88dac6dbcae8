#!/usr/bin/env python3
"""
<PERSON>ript to upload 100 random images from the dataset to the semantic search application.
Uses the FastAPI endpoints with AWS Bedrock integration for automatic description generation.
"""

import os
import sys
import random
import requests
import time
from pathlib import Path
from typing import List, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATASET_PATH = "/home/<USER>/dropi-ubuntu/content-moderation-api/dataset-50k-dropi"
API_BASE_URL = "http://localhost:8000"
COMPANY_NAME = "COLOMBIA"
TARGET_IMAGE_COUNT = 100  # Upload 100 images for robust testing
BATCH_SIZE = 3  # Upload in small batches to avoid overwhelming the services

# Supported image extensions
IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}

def find_all_images(dataset_path: str) -> List[str]:
    """Find all image files in the dataset directory."""
    print(f"🔍 Scanning dataset directory: {dataset_path}")
    image_files = []
    
    try:
        # Walk through all subdirectories
        for root, dirs, files in os.walk(dataset_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                # Check if it's an image file
                if file_ext in IMAGE_EXTENSIONS:
                    image_files.append(file_path)
                    
                    # Progress indicator
                    if len(image_files) % 1000 == 0:
                        print(f"   Found {len(image_files)} images so far...")
                        
    except Exception as e:
        print(f"❌ Error scanning dataset: {e}")
        return []
    
    print(f"✅ Found {len(image_files)} total images in dataset")
    return image_files

def select_random_images(image_files: List[str], count: int) -> List[str]:
    """Randomly select a specified number of images."""
    if len(image_files) < count:
        print(f"⚠️  Only {len(image_files)} images available, using all of them")
        return image_files
    
    selected = random.sample(image_files, count)
    print(f"✅ Selected {len(selected)} random images for upload")
    return selected

def upload_image(image_path: str, company_name: str) -> Tuple[bool, str]:
    """Upload a single image to the semantic search application using the correct workflow."""
    try:
        filename = os.path.basename(image_path)

        # Step 1: Get next product ID
        product_id_url = f"{API_BASE_URL}/api/v1/companies/{company_name}/next-product-id"
        product_id_response = requests.get(product_id_url, timeout=30)

        if product_id_response.status_code != 200:
            return False, f"Failed to get product ID: HTTP {product_id_response.status_code}"

        product_id_result = product_id_response.json()
        if not product_id_result.get('success', False):
            return False, f"Failed to get product ID: {product_id_result.get('message', 'Unknown error')}"

        product_id = product_id_result.get('next_product_id', 1)

        # Step 2: Generate description from image
        with open(image_path, 'rb') as f:
            files = {'file': (filename, f, 'image/jpeg')}
            data = {'company': company_name}

            desc_url = f"{API_BASE_URL}/api/v1/files/generate-description"
            desc_response = requests.post(desc_url, files=files, data=data, timeout=120)

            if desc_response.status_code != 200:
                return False, f"Description generation failed: HTTP {desc_response.status_code}"

            desc_result = desc_response.json()
            if not desc_result.get('success', False):
                return False, f"Description generation failed: {desc_result.get('message', 'Unknown error')}"

            description = desc_result.get('descripcion', '')
            if not description:
                return False, "No description generated"

        # Step 3: Upload file to get URL
        with open(image_path, 'rb') as f:
            files = {'file': (filename, f, 'image/jpeg')}
            data = {
                'company': company_name,
                'description': description,
                'product_id': product_id,
                'photo_id': 1
            }

            upload_url = f"{API_BASE_URL}/api/v1/files/upload"
            upload_response = requests.post(upload_url, files=files, data=data, timeout=120)

            if upload_response.status_code != 200:
                return False, f"File upload failed: HTTP {upload_response.status_code}"

            upload_result = upload_response.json()
            if not upload_result.get('success', False):
                return False, f"File upload failed: {upload_result.get('message', 'Unknown error')}"

            file_url = upload_result.get('file_url', '')

        # Step 4: Insert image record with description and URL
        insert_data = {
            "idProducto": product_id,
            "idPhoto": 1,
            "urlPhoto": file_url,
            "texto": description
        }

        insert_url = f"{API_BASE_URL}/api/v1/companies/{company_name}/images"
        insert_response = requests.post(insert_url, json=insert_data, timeout=120)

        if insert_response.status_code == 200:
            insert_result = insert_response.json()
            if insert_result.get('success', False):
                return True, f"Product {product_id}: {description[:50]}..."
            else:
                return False, f"Image insertion failed: {insert_result.get('message', 'Unknown error')}"
        else:
            return False, f"Image insertion failed: HTTP {insert_response.status_code}"

    except Exception as e:
        return False, f"Exception: {str(e)}"

def upload_images_batch(image_paths: List[str], company_name: str) -> None:
    """Upload images in batches with progress tracking."""
    total_images = len(image_paths)
    successful_uploads = 0
    failed_uploads = 0
    
    print(f"\n🚀 Starting upload of {total_images} images to company '{company_name}'")
    print(f"📦 Processing in batches of {BATCH_SIZE} images")
    print("=" * 60)
    
    for i in range(0, total_images, BATCH_SIZE):
        batch = image_paths[i:i + BATCH_SIZE]
        batch_num = (i // BATCH_SIZE) + 1
        total_batches = (total_images + BATCH_SIZE - 1) // BATCH_SIZE
        
        print(f"\n📦 Batch {batch_num}/{total_batches} ({len(batch)} images)")
        
        for j, image_path in enumerate(batch):
            image_name = os.path.basename(image_path)
            print(f"   [{j+1}/{len(batch)}] Uploading: {image_name}")
            
            success, message = upload_image(image_path, company_name)
            
            if success:
                successful_uploads += 1
                print(f"      ✅ Success: {message}")
            else:
                failed_uploads += 1
                print(f"      ❌ Failed: {message}")
            
            # Small delay between uploads to be gentle on the services
            time.sleep(1)
        
        # Longer delay between batches
        if i + BATCH_SIZE < total_images:
            print(f"   ⏳ Waiting 5 seconds before next batch...")
            time.sleep(5)
    
    # Final summary
    print("\n" + "=" * 60)
    print(f"📊 Upload Summary:")
    print(f"   ✅ Successful uploads: {successful_uploads}")
    print(f"   ❌ Failed uploads: {failed_uploads}")
    print(f"   📈 Success rate: {(successful_uploads/total_images)*100:.1f}%")
    print("=" * 60)

def main():
    """Main function to orchestrate the image upload process."""
    print("🎯 Semantic Search Image Upload Tool")
    print("=" * 60)
    
    # Check if the dataset directory exists
    if not os.path.exists(DATASET_PATH):
        print(f"❌ Dataset directory not found: {DATASET_PATH}")
        sys.exit(1)
    
    # Check if the API is accessible
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        if response.status_code != 200:
            print(f"❌ API health check failed: {response.status_code}")
            sys.exit(1)
        print("✅ API health check passed")
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        sys.exit(1)
    
    # Find all images in the dataset
    all_images = find_all_images(DATASET_PATH)
    if not all_images:
        print("❌ No images found in dataset")
        sys.exit(1)
    
    # Select random images
    selected_images = select_random_images(all_images, TARGET_IMAGE_COUNT)
    
    # Upload the selected images
    upload_images_batch(selected_images, COMPANY_NAME)
    
    print(f"\n🎉 Upload process completed!")
    print(f"💡 You can now test semantic search with the uploaded images")

if __name__ == "__main__":
    main()
