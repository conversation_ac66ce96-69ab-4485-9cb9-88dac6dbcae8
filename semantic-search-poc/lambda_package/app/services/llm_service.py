import os
import json
import base64
from typing import Optional
import boto3
from botocore.config import Config
from botocore.exceptions import Client<PERSON>rror
from dotenv import load_dotenv

load_dotenv()

class LLMService:
    def __init__(self):
        # AWS configuration
        aws_region = os.getenv("AWS_REGION", "us-east-2")
        aws_profile = os.getenv("AWS_PROFILE", "IA")

        # Configure boto3 session with profile
        session = boto3.Session(profile_name=aws_profile)

        # Configure retry settings for robustness
        retry_config = Config(
            region_name=aws_region,
            retries={
                "max_attempts": 10,
                "mode": "standard",
            },
        )

        # Initialize Bedrock runtime client
        self.client = session.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            config=retry_config
        )

        # Use Nova Lite - the cheapest multimodal model available in Bedrock for vision tasks
        # Nova Micro is text-only, so we use Nova Lite for image analysis
        self.model_id = "us.amazon.nova-lite-v1:0"
    
    def generate_image_description(self, image_bytes: Optional[bytes] = None,
                                 existing_text: Optional[str] = None) -> Optional[str]:
        """
        Generate a detailed product description using Amazon Nova Lite (cheapest multimodal model)

        Args:
            image_bytes: Image file bytes for vision analysis
            existing_text: Existing text description to enhance

        Returns:
            Generated description or None if error
        """
        try:
            if image_bytes:
                # Generate description from image using Nova Micro vision capabilities
                encoded_image = base64.b64encode(image_bytes).decode('utf-8')

                user_prompt = """Describe este producto en español con detalles específicos sobre:
                - Tipo de producto
                - Colores principales
                - Material aparente
                - Estilo o características distintivas
                - Uso o función

                Responde solo con la descripción del producto, sin explicaciones adicionales."""

                # Prepare request body for Nova Lite with image
                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "image": {
                                        "format": "jpeg",
                                        "source": {
                                            "bytes": encoded_image
                                        }
                                    }
                                },
                                {
                                    "text": user_prompt
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 300,
                        "temperature": 0.2
                    }
                }
            elif existing_text:
                # Enhance existing text description
                user_prompt = f"""
                Dado el siguiente texto de descripción de un producto, genera una descripción más detallada
                y completa en español que sea útil para búsqueda semántica. Incluye características como:
                - Color, material, estilo
                - Categoría del producto
                - Características distintivas
                - Uso o propósito

                Texto original: "{existing_text}"

                Responde únicamente con la descripción mejorada, sin explicaciones adicionales.
                """

                # Prepare request body for Nova Lite text-only
                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "text": f"Eres un experto en descripción de productos para e-commerce. {user_prompt}"
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 200,
                        "temperature": 0.3
                    }
                }
            else:
                return "Error: Se requiere imagen o texto para generar descripción"

            # Call Nova Lite using invoke_model
            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )

            # Parse response
            response_body = json.loads(response['body'].read())
            description = response_body['output']['message']['content'][0]['text'].strip()

            # Validate that the description is not empty
            if not description or len(description) < 10:
                return "Producto visible en la imagen"

            return description

        except ClientError as e:
            error_code = e.response['Error']['Code']
            print(f"Error de AWS Bedrock: {error_code} - {e}")
            if error_code == 'ValidationException':
                return "Error: formato de imagen no válido"
            elif error_code == 'ThrottlingException':
                return "Error: servicio temporalmente no disponible"
            else:
                return "Error al analizar la imagen"
        except Exception as e:
            print(f"Error generating image description with Nova Lite: {e}")
            return None
    
    def clean_search_query(self, query: str) -> tuple[str, str]:
        """
        Clean search query and extract price information using Nova Lite

        Args:
            query: Original search query

        Returns:
            Tuple of (cleaned_query, price_info)
        """
        try:
            # Extract price information using invoke_model
            price_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": f"""
                                Dado el siguiente texto de búsqueda, extrae ÚNICAMENTE información de precios
                                (precio mínimo y máximo si están mencionados). Si no hay información de precios,
                                responde "Sin información de precios".

                                Texto: "{query}"
                                """
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 50,
                    "temperature": 0.1
                }
            }

            price_response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(price_request),
                contentType="application/json"
            )

            price_body = json.loads(price_response['body'].read())
            price_info = price_body['output']['message']['content'][0]['text'].strip()

            # Clean query by removing price references using invoke_model
            clean_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": f"""
                                Dado el siguiente texto de búsqueda, elimina toda referencia a precios y
                                devuelve únicamente la descripción del producto buscado.

                                Texto original: "{query}"

                                Responde únicamente con el texto limpio, sin explicaciones.
                                """
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 100,
                    "temperature": 0.1
                }
            }

            clean_response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(clean_request),
                contentType="application/json"
            )

            clean_body = json.loads(clean_response['body'].read())
            clean_query = clean_body['output']['message']['content'][0]['text'].strip()

            return clean_query, price_info

        except Exception as e:
            print(f"Error cleaning search query with Nova Lite: {e}")
            return query, "Error al procesar precios"
    
    def test_connection(self) -> bool:
        """
        Test connection to AWS Bedrock Nova Lite

        Returns:
            True if connection successful, False otherwise
        """
        try:
            test_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": "test"
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 5
                }
            }

            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(test_request),
                contentType="application/json"
            )
            return True

        except Exception as e:
            print(f"AWS Bedrock Nova Lite connection test failed: {e}")
            return False

# Global LLM service instance
llm_service = LLMService()